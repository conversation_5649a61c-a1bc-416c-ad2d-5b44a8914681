{"name": "patent-nft-marketplace", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "compile": "hardhat compile", "test": "hardhat test", "test:verbose": "hardhat test --verbose", "test:gas": "REPORT_GAS=true hardhat test", "test:coverage": "hardhat coverage", "test:patent": "hardhat test test/PatentNFT.test.cjs", "test:psp": "hardhat test test/PSPToken.test.cjs", "test:payment": "hardhat test test/SearchPayment.test.cjs", "test:integration": "hardhat test test/Integration.test.cjs", "deploy": "hardhat run scripts/deploy.js --network localhost", "deploy-psp": "hardhat run scripts/deployPSP.js --network localhost", "deploy-search-payment": "hardhat run scripts/deploySearchPayment.js --network localhost", "setup-psp-auth": "hardhat run scripts/setupPSPAuthorization.js --network localhost", "deploy-psp-testnet": "hardhat run scripts/deployPSP.js --network sepolia", "deploy-search-payment-testnet": "hardhat run scripts/deploySearchPayment.js --network sepolia", "setup-psp-auth-testnet": "hardhat run scripts/setupPSPAuthorization.js --network sepolia", "node": "hardhat node", "deploy-pages": "npm run build && gh-pages -d dist"}, "dependencies": {"@openzeppelin/contracts": "^5.3.0", "axios": "^1.10.0", "date-fns": "^4.1.0", "ethers": "^6.14.0", "framer-motion": "^12.23.1", "lucide-react": "^0.525.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.8.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@nomicfoundation/hardhat-chai-matchers": "^2.0.0", "@nomicfoundation/hardhat-ethers": "^3.0.0", "@nomicfoundation/hardhat-toolbox": "^3.0.0", "@typechain/hardhat": "^8.0.0", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "dotenv": "^16.3.1", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "gh-pages": "^6.3.0", "globals": "^15.9.0", "hardhat": "^2.17.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}