# AI API Keys
VITE_GEMINI_API_KEY=your_gemini_api_key_here

# USPTO API (optional for development)
VITE_USPTO_API_KEY=your_uspto_key_when_available

# Contract Addresses (auto-populated after deployment)
VITE_PSP_TOKEN_ADDRESS=
VITE_SEARCH_PAYMENT_ADDRESS=
VITE_PATENT_NFT_ADDRESS=

# Network Configuration
VITE_NETWORK_NAME=localhost
VITE_CHAIN_ID=31337

# Deployment Configuration (for deploying contracts)
PRIVATE_KEY=your_wallet_private_key_for_deployment
INFURA_PROJECT_ID=your_infura_project_id
SEPOLIA_RPC_URL=https://sepolia.infura.io/v3/your_infura_project_id
MAINNET_RPC_URL=https://mainnet.infura.io/v3/your_infura_project_id

# Optional: For contract verification
ETHERSCAN_API_KEY=your_etherscan_api_key

# Optional: For gas reporting
REPORT_GAS=true

# Development Configuration
VITE_ENABLE_MOCK_DATA=false